NODE_ENV=development
NUXT_PUBLIC_ENV=development
# your website url e.g: mail.aitre.cc
NUXT_PUBLIC_BASE_URL=
# your domain url e.g:aitre.cc
NUXT_PUBLIC_DOMAIN_URL=
# 支持的域名列表，用逗号分隔 e.g: domain1.com,domain2.com,domain3.com
NUXT_PUBLIC_AVAILABLE_DOMAINS=
# App name
NUXT_PUBLIC_APP_NAME=Your App Name
# Server port
PORT=3000
# Google Search Console verifyCode
GOOGLE_SEARCH_CONSOLE_VERIFICATION_CODE=

# 隐私保护配置 - 服务端密码验证（可选，推荐）
# 设置后，用户需要输入密码才能访问临时邮箱服务
# 例如: ACCESS_PASSWORD=mySecurePassword123
ACCESS_PASSWORD=

# ⚠️ 安全提醒：
# - 不要使用 NUXT_PUBLIC_ACCESS_PASSWORD，那会暴露到前端！
# - ACCESS_PASSWORD 只在服务端可用，更安全
# - 如果不需要密码保护，请保持此项为空

# IMAP 邮箱配置 - 用于接收转发到您域名的邮件
# IMAP 服务器地址 (例如: imap.gmail.com, imap.qq.com)
IMAP_HOST=imap.example.com
# IMAP 端口 (通常 993 用于 SSL, 143 用于非 SSL)
IMAP_PORT=993
# 您的邮箱用户名
IMAP_USERNAME=<EMAIL>
# 您的邮箱密码或授权码
IMAP_PASSWORD=your-password
# 启用 TLS 加密 (true/false)
IMAP_TLS=true

# 📧 IMAP 配置说明：
# 1. 需要一个真实的邮箱来接收转发邮件
# 2. 对于 Gmail，需要使用应用专用密码
# 3. 对于 QQ 邮箱，需要使用授权码
# 4. 确保邮箱开启了 IMAP 服务



